# API配置说明

## 概述

本目录包含前端应用的API配置文件，用于管理对后端服务的请求配置。

## 文件说明

### apiConfig.ts

主要的API配置文件，包含：

- **ApiConfig接口**: 定义API配置的类型结构
- **环境配置**: 支持不同环境下的配置
- **API端点常量**: 预定义的API路径
- **工具函数**: 用于构建完整的API URL

## 配置方式

### 1. 环境变量配置

在项目根目录的环境变量文件中设置：

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8000

# .env.production  
VITE_API_BASE_URL=https://api.yourserver.com

# .env.test
VITE_API_BASE_URL=http://test-server:8000
```

### 2. 代码中使用

```typescript
import { buildApiUrl, API_ENDPOINTS } from '@/config/apiConfig';

// 使用预定义的端点
const workspacesUrl = buildApiUrl(API_ENDPOINTS.WORKSPACES);

// 使用自定义端点
const customUrl = buildApiUrl('/api/custom-endpoint');
```

## API端点

当前支持的API端点：

- `WORKSPACES`: `/api/workspaces` - 获取工作区列表
- `WORKSPACE_FILES`: `/api/workspaces/{id}/files` - 获取工作区文件
- `FILES`: `/api/files` - 文件操作
- `SEARCH`: `/api/search` - 搜索功能

## 环境支持

- **development**: 开发环境，默认使用 localhost:8000
- **production**: 生产环境，使用环境变量 VITE_API_BASE_URL
- **test**: 测试环境，使用 localhost:8000

## 修改配置

1. **修改默认配置**: 编辑 `apiConfig.ts` 中的 `defaultConfig`
2. **添加新环境**: 在 `envConfigs` 中添加新的环境配置
3. **添加新端点**: 在 `API_ENDPOINTS` 中添加新的API路径

## 注意事项

- 所有API请求都应该使用 `buildApiUrl()` 函数构建URL
- 不要在代码中硬编码API地址
- 环境变量必须以 `VITE_` 前缀开头才能在前端代码中访问
- 修改配置后需要重启开发服务器
