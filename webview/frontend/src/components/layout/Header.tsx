// import React from 'react'; // React 19+ 不需要显式导入 React
import { Button } from '@/components/ui';
import { PanelLeftClose, PanelLeftOpen, PanelRightClose, PanelRightOpen } from 'lucide-react';

interface HeaderProps {
  title?: string;
  leftPanelCollapsed?: boolean;
  rightPanelCollapsed?: boolean;
  onToggleLeftPanel?: () => void;
  onToggleRightPanel?: () => void;
}

export function Header({
  title = 'WebView - DeepSearch 可视化工具',
  leftPanelCollapsed = false,
  rightPanelCollapsed = false,
  onToggleLeftPanel,
  onToggleRightPanel,
}: HeaderProps) {
  return (
    <header className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
      <div className="flex items-center gap-3">
        {onToggleLeftPanel && (
          <Button
            variant="ghost"
            size="sm"
            icon={leftPanelCollapsed ? <PanelLeftOpen className="h-4 w-4" /> : <PanelLeftClose className="h-4 w-4" />}
            onClick={onToggleLeftPanel}
          >
            {leftPanelCollapsed ? '显示' : '隐藏'}工作区
          </Button>
        )}
        <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
      </div>

      <div className="flex items-center gap-2">
        {onToggleRightPanel && (
          <Button
            variant="ghost"
            size="sm"
            icon={rightPanelCollapsed ? <PanelRightOpen className="h-4 w-4" /> : <PanelRightClose className="h-4 w-4" />}
            onClick={onToggleRightPanel}
          >
            {rightPanelCollapsed ? '显示' : '隐藏'}搜索
          </Button>
        )}
      </div>
    </header>
  );
}
