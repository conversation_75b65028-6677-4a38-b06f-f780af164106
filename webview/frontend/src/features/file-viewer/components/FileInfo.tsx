// import React from 'react'; // React 19+ 不需要显式导入 React
import { cn } from '@/utils/cn';
import { Button } from '@/components/ui';
import { 
  File, 
  Calendar, 
  HardDrive, 
  Copy, 
  ExternalLink,
  RefreshCw 
} from 'lucide-react';
import { formatFileSize, formatDate } from '@/utils/format';
import type { FileNode } from '@/types';

interface FileInfoProps {
  file?: FileNode;
  filePath?: string;
  className?: string;
  onReload?: () => void;
}

export function FileInfo({ file, filePath, className, onReload }: FileInfoProps) {
  const handleCopyPath = () => {
    if (filePath) {
      navigator.clipboard.writeText(filePath);
      // 这里可以添加一个 toast 通知
    }
  };

  const handleOpenExternal = () => {
    if (filePath) {
      // 在实际应用中，这里可以调用系统 API 打开文件
      console.log('Open file externally:', filePath);
    }
  };

  if (!file && !filePath) {
    return null;
  }

  return (
    <div className={cn(
      'bg-gray-50 border-b border-gray-200 px-4 py-3',
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <File className="h-4 w-4 text-gray-500 flex-shrink-0" />
          
          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium text-gray-900 truncate">
              {file?.name || filePath?.split('/').pop() || '未知文件'}
            </div>
            <div className="text-xs text-gray-500 truncate">
              {filePath || file?.path}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1 flex-shrink-0">
          {onReload && (
            <Button
              variant="ghost"
              size="sm"
              icon={<RefreshCw className="h-3 w-3" />}
              onClick={onReload}
            >
              刷新
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            icon={<Copy className="h-3 w-3" />}
            onClick={handleCopyPath}
          >
            复制路径
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            icon={<ExternalLink className="h-3 w-3" />}
            onClick={handleOpenExternal}
          >
            外部打开
          </Button>
        </div>
      </div>

      {file && (
        <div className="flex items-center gap-6 mt-2 text-xs text-gray-500">
          {file.size && (
            <div className="flex items-center gap-1">
              <HardDrive className="h-3 w-3" />
              <span>{formatFileSize(file.size)}</span>
            </div>
          )}
          
          {file.lastModified && (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(file.lastModified)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
