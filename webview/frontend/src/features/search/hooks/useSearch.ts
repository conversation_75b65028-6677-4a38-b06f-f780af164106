import { useState, useCallback } from 'react';
import { searchService } from '@/services/searchService';
import type { SearchRequest } from '@/types';

export function useSearch() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastQuery, setLastQuery] = useState<string>('');
  const [searchTime, setSearchTime] = useState<number>(0);
  const [progressMessages, setProgressMessages] = useState<string[]>([]);

  // 执行流式搜索（显示过程）
  const executeSearch = useCallback(async (request: SearchRequest) => {
    setLoading(true);
    setError(null);
    setLastQuery(request.query);
    setProgressMessages([]);
    setResult('');

    const startTime = Date.now();

    try {
      await searchService.executeSearch(
        request,
        // onProgress
        (message: string) => {
          setProgressMessages(prev => [...prev, message]);
        },
        // onComplete
        (_finalResult: string) => { // 重命名为 _finalResult 表示未使用
          // 不设置结果内容，只标记完成
          setResult(''); // 保持空白，不显示结果
          setSearchTime(Date.now() - startTime);
          setLoading(false); // 重要：停止加载状态
        },
        // onError
        (errorMsg: string) => {
          setError(errorMsg);
          setLoading(false);
        }
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
      setLoading(false);
    }
  }, []);

  // 清空搜索结果
  const clearResults = useCallback(() => {
    setResult('');
    setError(null);
    setLastQuery('');
    setSearchTime(0);
    setProgressMessages([]);
  }, []);

  return {
    result,
    loading,
    error,
    lastQuery,
    searchTime,
    progressMessages,
    executeSearch,
    clearResults,
  };
}
