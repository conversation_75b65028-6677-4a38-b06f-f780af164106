# 后端路径变量加载机制详解

## 🔄 加载流程

### 1. 环境变量读取
```typescript
// 在 apiConfig.ts 中
const envConfigs: Record<string, Partial<ApiConfig>> = {
  development: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  },
  production: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  },
  test: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  },
};
```

### 2. 环境检测
```typescript
// 获取当前运行环境
const getCurrentEnv = (): string => {
  return import.meta.env.MODE || 'development';
};
```

### 3. 配置合并
```typescript
// 合并默认配置和环境特定配置
const createConfig = (): ApiConfig => {
  const env = getCurrentEnv();
  const envConfig = envConfigs[env] || {};
  
  return {
    ...defaultConfig,    // 默认配置
    ...envConfig,       // 环境特定配置（会覆盖默认配置）
  };
};
```

## 📁 环境变量文件

### .env.development
```bash
# 开发环境配置
VITE_API_BASE_URL=http://localhost:8000
```

### .env.production
```bash
# 生产环境配置
VITE_API_BASE_URL=http://localhost:8000
```

### .env
```bash
# 默认配置（所有环境的后备配置）
VITE_API_BASE_URL=http://localhost:8000
```

## 🎯 实际使用

### 在服务中使用
```typescript
// fileSystem.ts
import { buildApiUrl, API_ENDPOINTS } from '@/config/apiConfig';

// 自动使用配置的baseUrl
const response = await fetch(buildApiUrl(API_ENDPOINTS.WORKSPACES));
```

### 运行时配置解析
1. **开发模式** (`npm run dev`):
   - 环境: `development`
   - 读取: `.env.development` → `.env`
   - 结果: `http://localhost:8000`

2. **生产构建** (`npm run build`):
   - 环境: `production`
   - 读取: `.env.production` → `.env`
   - 结果: `http://localhost:8000`

## 🔧 修改配置的方法

### 方法1: 修改环境变量文件
```bash
# .env.development
VITE_API_BASE_URL=http://your-dev-server:8000

# .env.production
VITE_API_BASE_URL=https://api.yourserver.com
```

### 方法2: 运行时环境变量
```bash
# 临时覆盖配置
VITE_API_BASE_URL=http://localhost:9000 npm run dev
```

### 方法3: 创建本地配置文件
```bash
# .env.local (不会被git跟踪)
VITE_API_BASE_URL=http://localhost:3001
```

## ⚠️ 重要注意事项

1. **环境变量前缀**: 必须以 `VITE_` 开头才能在前端访问
2. **重启要求**: 修改环境变量后需要重启开发服务器
3. **构建时绑定**: 环境变量在构建时被静态替换，不是运行时动态读取
4. **安全性**: 不要在前端环境变量中存储敏感信息

## 🔍 调试配置

### 查看当前配置
```typescript
// 在浏览器控制台中
console.log('Current API Config:', import.meta.env.VITE_API_BASE_URL);
console.log('Environment:', import.meta.env.MODE);
```

### 验证API调用
```typescript
// 在浏览器控制台中
import { buildApiUrl, API_ENDPOINTS } from './src/config/apiConfig';
console.log('Workspaces URL:', buildApiUrl(API_ENDPOINTS.WORKSPACES));
```
