{"version": 3, "sources": ["../../refractor/lang/warpscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = warpscript\nwarpscript.displayName = 'warpscript'\nwarpscript.aliases = []\nfunction warpscript(Prism) {\n  Prism.languages.warpscript = {\n    comment: /#.*|\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern:\n        /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'|<'(?:[^\\\\']|'(?!>)|\\\\.)*'>/,\n      greedy: true\n    },\n    variable: /\\$\\S+/,\n    macro: {\n      pattern: /@\\S+/,\n      alias: 'property'\n    },\n    // WarpScript doesn't have any keywords, these are all functions under the control category\n    // https://www.warp10.io/tags/control\n    keyword:\n      /\\b(?:BREAK|CHECKMACRO|CONTINUE|CUDF|DEFINED|DEFINEDMACRO|EVAL|FAIL|FOR|FOREACH|FORSTEP|IFT|IFTE|MSGFAIL|NRETURN|RETHROW|RETURN|SWITCH|TRY|UDF|UNTIL|WHILE)\\b/,\n    number:\n      /[+-]?\\b(?:NaN|Infinity|\\d+(?:\\.\\d*)?(?:[Ee][+-]?\\d+)?|0x[\\da-fA-F]+|0b[01]+)\\b/,\n    boolean: /\\b(?:F|T|false|true)\\b/,\n    punctuation: /<%|%>|[{}[\\]()]/,\n    // Some operators from the \"operators\" category\n    // https://www.warp10.io/tags/operators\n    operator:\n      /==|&&?|\\|\\|?|\\*\\*?|>>>?|<<|[<>!~]=?|[-/%^]|\\+!?|\\b(?:AND|NOT|OR)\\b/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC;AACtB,aAAS,WAAW,OAAO;AACzB,YAAM,UAAU,aAAa;AAAA,QAC3B,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,QACV,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA;AAAA;AAAA,QAGA,SACE;AAAA,QACF,QACE;AAAA,QACF,SAAS;AAAA,QACT,aAAa;AAAA;AAAA;AAAA,QAGb,UACE;AAAA,MACJ;AAAA,IACF;AAAA;AAAA;", "names": []}