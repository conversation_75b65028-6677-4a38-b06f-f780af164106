{"version": 3, "sources": ["../../highlight.js/lib/languages/sql_more.js"], "sourcesContent": ["/*\n Language: SQL More (mix of MySQL, Oracle, etc)\n Contributors: <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> August <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <vadi<PERSON><PERSON>@yahoo.com>, <PERSON> <<EMAIL>>\n Website: https://en.wikipedia.org/wiki/SQL\n Category: database\n */\n\n/*\n\nThis is a preservation of the old bloated SQL grammar which includes pretty much\nthe kitchen sink because no one was keeping track of which keywords belong to\nwhich databases.  This is likely to be removed in the future.\n\n- Oracle SQL should be factored into it's own 3rd party grammar.\n- MySQL should be factored out into it's own 3rd party grammar.\n\n*/\n\nfunction sql_more(hljs) {\n  var COMMENT_MODE = hljs.COMMENT('--', '$');\n  return {\n    name: 'SQL (more)',\n    aliases: [\"mysql\", \"oracle\"],\n    disableAutodetect: true,\n    case_insensitive: true,\n    illegal: /[<>{}*]/,\n    contains: [\n      {\n        beginKeywords:\n          'begin end start commit rollback savepoint lock alter create drop rename call ' +\n          'delete do handler insert load replace select truncate update set show pragma grant ' +\n          'merge describe use explain help declare prepare execute deallocate release ' +\n          'unlock purge reset change stop analyze cache flush optimize repair kill ' +\n          'install uninstall checksum restore check backup revoke comment values with',\n        end: /;/, endsWithParent: true,\n        keywords: {\n          $pattern: /[\\w\\.]+/,\n          keyword:\n            'as abort abs absolute acc acce accep accept access accessed accessible account acos action activate add ' +\n            'addtime admin administer advanced advise aes_decrypt aes_encrypt after agent aggregate ali alia alias ' +\n            'all allocate allow alter always analyze ancillary and anti any anydata anydataset anyschema anytype apply ' +\n            'archive archived archivelog are as asc ascii asin assembly assertion associate asynchronous at atan ' +\n            'atn2 attr attri attrib attribu attribut attribute attributes audit authenticated authentication authid ' +\n            'authors auto autoallocate autodblink autoextend automatic availability avg backup badfile basicfile ' +\n            'before begin beginning benchmark between bfile bfile_base big bigfile bin binary_double binary_float ' +\n            'binlog bit_and bit_count bit_length bit_or bit_xor bitmap blob_base block blocksize body both bound ' +\n            'bucket buffer_cache buffer_pool build bulk by byte byteordermark bytes cache caching call calling cancel ' +\n            'capacity cascade cascaded case cast catalog category ceil ceiling chain change changed char_base ' +\n            'char_length character_length characters characterset charindex charset charsetform charsetid check ' +\n            'checksum checksum_agg child choose chr chunk class cleanup clear client clob clob_base clone close ' +\n            'cluster_id cluster_probability cluster_set clustering coalesce coercibility col collate collation ' +\n            'collect colu colum column column_value columns columns_updated comment commit compact compatibility ' +\n            'compiled complete composite_limit compound compress compute concat concat_ws concurrent confirm conn ' +\n            'connec connect connect_by_iscycle connect_by_isleaf connect_by_root connect_time connection ' +\n            'consider consistent constant constraint constraints constructor container content contents context ' +\n            'contributors controlfile conv convert convert_tz corr corr_k corr_s corresponding corruption cos cost ' +\n            'count count_big counted covar_pop covar_samp cpu_per_call cpu_per_session crc32 create creation ' +\n            'critical cross cube cume_dist curdate current current_date current_time current_timestamp current_user ' +\n            'cursor curtime customdatum cycle data database databases datafile datafiles datalength date_add ' +\n            'date_cache date_format date_sub dateadd datediff datefromparts datename datepart datetime2fromparts ' +\n            'day day_to_second dayname dayofmonth dayofweek dayofyear days db_role_change dbtimezone ddl deallocate ' +\n            'declare decode decompose decrement decrypt deduplicate def defa defau defaul default defaults ' +\n            'deferred defi defin define degrees delayed delegate delete delete_all delimited demand dense_rank ' +\n            'depth dequeue des_decrypt des_encrypt des_key_file desc descr descri describ describe descriptor ' +\n            'deterministic diagnostics difference dimension direct_load directory disable disable_all ' +\n            'disallow disassociate discardfile disconnect diskgroup distinct distinctrow distribute distributed div ' +\n            'do document domain dotnet double downgrade drop dumpfile duplicate duration each edition editionable ' +\n            'editions element ellipsis else elsif elt empty enable enable_all enclosed encode encoding encrypt ' +\n            'end end-exec endian enforced engine engines enqueue enterprise entityescaping eomonth error errors ' +\n            'escaped evalname evaluate event eventdata events except exception exceptions exchange exclude excluding ' +\n            'execu execut execute exempt exists exit exp expire explain explode export export_set extended extent external ' +\n            'external_1 external_2 externally extract failed failed_login_attempts failover failure far fast ' +\n            'feature_set feature_value fetch field fields file file_name_convert filesystem_like_logging final ' +\n            'finish first first_value fixed flash_cache flashback floor flush following follows for forall force foreign ' +\n            'form forma format found found_rows freelist freelists freepools fresh from from_base64 from_days ' +\n            'ftp full function general generated get get_format get_lock getdate getutcdate global global_name ' +\n            'globally go goto grant grants greatest group group_concat group_id grouping grouping_id groups ' +\n            'gtid_subtract guarantee guard handler hash hashkeys having hea head headi headin heading heap help hex ' +\n            'hierarchy high high_priority hosts hour hours http id ident_current ident_incr ident_seed identified ' +\n            'identity idle_time if ifnull ignore iif ilike ilm immediate import in include including increment ' +\n            'index indexes indexing indextype indicator indices inet6_aton inet6_ntoa inet_aton inet_ntoa infile ' +\n            'initial initialized initially initrans inmemory inner innodb input insert install instance instantiable ' +\n            'instr interface interleaved intersect into invalidate invisible is is_free_lock is_ipv4 is_ipv4_compat ' +\n            'is_not is_not_null is_used_lock isdate isnull isolation iterate java join json json_exists ' +\n            'keep keep_duplicates key keys kill language large last last_day last_insert_id last_value lateral lax lcase ' +\n            'lead leading least leaves left len lenght length less level levels library like like2 like4 likec limit ' +\n            'lines link list listagg little ln load load_file lob lobs local localtime localtimestamp locate ' +\n            'locator lock locked log log10 log2 logfile logfiles logging logical logical_reads_per_call ' +\n            'logoff logon logs long loop low low_priority lower lpad lrtrim ltrim main make_set makedate maketime ' +\n            'managed management manual map mapping mask master master_pos_wait match matched materialized max ' +\n            'maxextents maximize maxinstances maxlen maxlogfiles maxloghistory maxlogmembers maxsize maxtrans ' +\n            'md5 measures median medium member memcompress memory merge microsecond mid migration min minextents ' +\n            'minimum mining minus minute minutes minvalue missing mod mode model modification modify module monitoring month ' +\n            'months mount move movement multiset mutex name name_const names nan national native natural nav nchar ' +\n            'nclob nested never new newline next nextval no no_write_to_binlog noarchivelog noaudit nobadfile ' +\n            'nocheck nocompress nocopy nocycle nodelay nodiscardfile noentityescaping noguarantee nokeep nologfile ' +\n            'nomapping nomaxvalue nominimize nominvalue nomonitoring none noneditionable nonschema noorder ' +\n            'nopr nopro noprom nopromp noprompt norely noresetlogs noreverse normal norowdependencies noschemacheck ' +\n            'noswitch not nothing notice notnull notrim novalidate now nowait nth_value nullif nulls num numb numbe ' +\n            'nvarchar nvarchar2 object ocicoll ocidate ocidatetime ociduration ociinterval ociloblocator ocinumber ' +\n            'ociref ocirefcursor ocirowid ocistring ocitype oct octet_length of off offline offset oid oidindex old ' +\n            'on online only opaque open operations operator optimal optimize option optionally or oracle oracle_date ' +\n            'oradata ord ordaudio orddicom orddoc order ordimage ordinality ordvideo organization orlany orlvary ' +\n            'out outer outfile outline output over overflow overriding package pad parallel parallel_enable ' +\n            'parameters parent parse partial partition partitions pascal passing password password_grace_time ' +\n            'password_lock_time password_reuse_max password_reuse_time password_verify_function patch path patindex ' +\n            'pctincrease pctthreshold pctused pctversion percent percent_rank percentile_cont percentile_disc ' +\n            'performance period period_add period_diff permanent physical pi pipe pipelined pivot pluggable plugin ' +\n            'policy position post_transaction pow power pragma prebuilt precedes preceding precision prediction ' +\n            'prediction_cost prediction_details prediction_probability prediction_set prepare present preserve ' +\n            'prior priority private private_sga privileges procedural procedure procedure_analyze processlist ' +\n            'profiles project prompt protection public publishingservername purge quarter query quick quiesce quota ' +\n            'quotename radians raise rand range rank raw read reads readsize rebuild record records ' +\n            'recover recovery recursive recycle redo reduced ref reference referenced references referencing refresh ' +\n            'regexp_like register regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy ' +\n            'reject rekey relational relative relaylog release release_lock relies_on relocate rely rem remainder rename ' +\n            'repair repeat replace replicate replication required reset resetlogs resize resource respect restore ' +\n            'restricted result result_cache resumable resume retention return returning returns reuse reverse revoke ' +\n            'right rlike role roles rollback rolling rollup round row row_count rowdependencies rowid rownum rows ' +\n            'rtrim rules safe salt sample save savepoint sb1 sb2 sb4 scan schema schemacheck scn scope scroll ' +\n            'sdo_georaster sdo_topo_geometry search sec_to_time second seconds section securefile security seed segment select ' +\n            'self semi sequence sequential serializable server servererror session session_user sessions_per_user set ' +\n            'sets settings sha sha1 sha2 share shared shared_pool short show shrink shutdown si_averagecolor ' +\n            'si_colorhistogram si_featurelist si_positionalcolor si_stillimage si_texture siblings sid sign sin ' +\n            'size size_t sizes skip slave sleep smalldatetimefromparts smallfile snapshot some soname sort soundex ' +\n            'source space sparse spfile split sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows ' +\n            'sql_small_result sql_variant_property sqlcode sqldata sqlerror sqlname sqlstate sqrt square standalone ' +\n            'standby start starting startup statement static statistics stats_binomial_test stats_crosstab ' +\n            'stats_ks_test stats_mode stats_mw_test stats_one_way_anova stats_t_test_ stats_t_test_indep ' +\n            'stats_t_test_one stats_t_test_paired stats_wsr_test status std stddev stddev_pop stddev_samp stdev ' +\n            'stop storage store stored str str_to_date straight_join strcmp strict string struct stuff style subdate ' +\n            'subpartition subpartitions substitutable substr substring subtime subtring_index subtype success sum ' +\n            'suspend switch switchoffset switchover sync synchronous synonym sys sys_xmlagg sysasm sysaux sysdate ' +\n            'sysdatetimeoffset sysdba sysoper system system_user sysutcdatetime table tables tablespace tablesample tan tdo ' +\n            'template temporary terminated tertiary_weights test than then thread through tier ties time time_format ' +\n            'time_zone timediff timefromparts timeout timestamp timestampadd timestampdiff timezone_abbr ' +\n            'timezone_minute timezone_region to to_base64 to_date to_days to_seconds todatetimeoffset trace tracking ' +\n            'transaction transactional translate translation treat trigger trigger_nestlevel triggers trim truncate ' +\n            'try_cast try_convert try_parse type ub1 ub2 ub4 ucase unarchived unbounded uncompress ' +\n            'under undo unhex unicode uniform uninstall union unique unix_timestamp unknown unlimited unlock unnest unpivot ' +\n            'unrecoverable unsafe unsigned until untrusted unusable unused update updated upgrade upped upper upsert ' +\n            'url urowid usable usage use use_stored_outlines user user_data user_resources users using utc_date ' +\n            'utc_timestamp uuid uuid_short validate validate_password_strength validation valist value values var ' +\n            'var_samp varcharc vari varia variab variabl variable variables variance varp varraw varrawc varray ' +\n            'verify version versions view virtual visible void wait wallet warning warnings week weekday weekofyear ' +\n            'wellformed when whene whenev wheneve whenever where while whitespace window with within without work wrapped ' +\n            'xdb xml xmlagg xmlattributes xmlcast xmlcolattval xmlelement xmlexists xmlforest xmlindex xmlnamespaces ' +\n            'xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltype xor year year_to_month years yearweek',\n          literal:\n            'true false null unknown',\n          built_in:\n            'array bigint binary bit blob bool boolean char character date dec decimal float int int8 integer interval number ' +\n            'numeric real record serial serial8 smallint text time timestamp tinyint varchar varchar2 varying void'\n        },\n        contains: [\n          {\n            className: 'string',\n            begin: '\\'', end: '\\'',\n            contains: [{begin: '\\'\\''}]\n          },\n          {\n            className: 'string',\n            begin: '\"', end: '\"',\n            contains: [{begin: '\"\"'}]\n          },\n          {\n            className: 'string',\n            begin: '`', end: '`'\n          },\n          hljs.C_NUMBER_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          COMMENT_MODE,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = sql_more;\n"], "mappings": ";;;;;AAAA;AAAA;AAkBA,aAAS,SAAS,MAAM;AACtB,UAAI,eAAe,KAAK,QAAQ,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,SAAS,QAAQ;AAAA,QAC3B,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,eACE;AAAA,YAKF,KAAK;AAAA,YAAK,gBAAgB;AAAA,YAC1B,UAAU;AAAA,cACR,UAAU;AAAA,cACV,SACE;AAAA,cA8GF,SACE;AAAA,cACF,UACE;AAAA,YAEJ;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAM,KAAK;AAAA,gBAClB,UAAU,CAAC,EAAC,OAAO,KAAM,CAAC;AAAA,cAC5B;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAK,KAAK;AAAA,gBACjB,UAAU,CAAC,EAAC,OAAO,KAAI,CAAC;AAAA,cAC1B;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAK,KAAK;AAAA,cACnB;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}