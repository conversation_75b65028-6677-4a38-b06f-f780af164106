{"version": 3, "sources": ["../../refractor/lang/j.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = j\nj.displayName = 'j'\nj.aliases = []\nfunction j(Prism) {\n  Prism.languages.j = {\n    comment: {\n      pattern: /\\bNB\\..*/,\n      greedy: true\n    },\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:(?:CR|LF|adverb|conjunction|def|define|dyad|monad|noun|verb)\\b|(?:assert|break|case|catch[dt]?|continue|do|else|elseif|end|fcase|for|for_\\w+|goto_\\w+|if|label_\\w+|return|select|throw|try|while|whilst)\\.)/,\n    verb: {\n      // Negative look-ahead prevents bad highlighting\n      // of ^: ;. =. =: !. !:\n      pattern:\n        /(?!\\^:|;\\.|[=!][.:])(?:\\{(?:\\.|::?)?|p(?:\\.\\.?|:)|[=!\\]]|[<>+*\\-%$|,#][.:]?|[?^]\\.?|[;\\[]:?|[~}\"i][.:]|[ACeEIjLor]\\.|(?:[_\\/\\\\qsux]|_?\\d):)/,\n      alias: 'keyword'\n    },\n    number:\n      /\\b_?(?:(?!\\d:)\\d+(?:\\.\\d+)?(?:(?:ad|ar|[ejpx])_?\\d+(?:\\.\\d+)?)*(?:b_?[\\da-z]+(?:\\.[\\da-z]+)?)?|_\\b(?!\\.))/,\n    adverb: {\n      pattern: /[~}]|[\\/\\\\]\\.?|[bfM]\\.|t[.:]/,\n      alias: 'builtin'\n    },\n    operator: /[=a][.:]|_\\./,\n    conjunction: {\n      pattern: /&(?:\\.:?|:)?|[.:@][.:]?|[!D][.:]|[;dHT]\\.|`:?|[\\^LS]:|\"/,\n      alias: 'variable'\n    },\n    punctuation: /[()]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,MAAE,cAAc;AAChB,MAAE,UAAU,CAAC;AACb,aAAS,EAAE,OAAO;AAChB,YAAM,UAAU,IAAI;AAAA,QAClB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,MAAM;AAAA;AAAA;AAAA,UAGJ,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,QACE;AAAA,QACF,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}