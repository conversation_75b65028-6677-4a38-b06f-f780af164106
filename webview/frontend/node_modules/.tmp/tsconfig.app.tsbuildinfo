{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/layout/header.tsx", "../../src/components/layout/mainlayout.tsx", "../../src/components/layout/sidebar.tsx", "../../src/components/layout/index.ts", "../../src/components/ui/button.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/loading.tsx", "../../src/components/ui/markdownrenderer.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/index.ts", "../../src/config/apiconfig.ts", "../../src/features/file-viewer/components/codeviewer.tsx", "../../src/features/file-viewer/components/fileinfo.tsx", "../../src/features/file-viewer/components/fileviewer.tsx", "../../src/features/file-viewer/components/index.ts", "../../src/features/file-viewer/hooks/index.ts", "../../src/features/file-viewer/hooks/usefilecontent.ts", "../../src/features/search/components/searchpanel.tsx", "../../src/features/search/components/index.ts", "../../src/features/search/hooks/index.ts", "../../src/features/search/hooks/usesearch.ts", "../../src/features/search/hooks/usesearchprocess.ts", "../../src/features/workspace/components/filetree.tsx", "../../src/features/workspace/components/settingspanel.tsx", "../../src/features/workspace/components/workspaceselector.tsx", "../../src/features/workspace/components/index.ts", "../../src/features/workspace/hooks/index.ts", "../../src/features/workspace/hooks/usefiletree.ts", "../../src/features/workspace/hooks/useworkspace.ts", "../../src/services/filesystem.ts", "../../src/services/searchservice.ts", "../../src/store/appstore.ts", "../../src/types/common.ts", "../../src/types/index.ts", "../../src/utils/cn.ts", "../../src/utils/format.ts"], "version": "5.7.3"}