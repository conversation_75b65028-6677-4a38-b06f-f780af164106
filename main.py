"""
DeepSearch 主入口文件
提供命令行接口和使用示例
"""

import os
import sys
import argparse
from deep_search import DeepSearch, LLMClient

# 确保输出立即刷新
import builtins
original_print = builtins.print

def flush_print(*args, **kwargs):
    original_print(*args, **kwargs)
    sys.stdout.flush()

# 替换全局print函数
builtins.print = flush_print


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DeepSearch - 仓库问答深度搜索工具")
    parser.add_argument("query", help="搜索查询")
    parser.add_argument("--repo-path", "-r", default=".", help="仓库路径 (默认: 当前目录)")
    parser.add_argument("--repo-info", "-i", default="", help="仓库信息描述")
    parser.add_argument("--search-type", "-s", choices=["grep", "embedding"], 
                       default="grep", help="搜索类型 (默认: grep)")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 检查仓库路径
    if not os.path.exists(args.repo_path):
        print(f"错误: 仓库路径不存在: {args.repo_path}")
        sys.exit(1)
    
    # 初始化DeepSearch
    deep_search = DeepSearch(
        repo_path=args.repo_path,
        repo_info=args.repo_info,
        search_type=args.search_type
    )
    
    # 执行搜索
    print(f"开始搜索: {args.query}")
    print("=" * 50)
    
    try:
        result = deep_search.search(args.query)
        
        # 如果指定了输出文件，才输出完整结果
        if args.output:
            output_content = format_result(result, args.verbose)
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output_content)
            print(f"\n结果已保存到: {args.output}")
        # 在命令行模式下不输出详细结果，已经在搜索过程中输出了过程信息
            
    except Exception as e:
        print(f"搜索失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def format_result(result, verbose=False):
    """
    格式化搜索结果
    
    Args:
        result: SearchResult对象
        verbose: 是否详细输出
        
    Returns:
        str: 格式化后的结果
    """
    lines = []
    
    # 摘要信息
    lines.append("## 搜索摘要")
    lines.append(result.get_summary())
    lines.append("")
    
    if verbose:
        # 详细查询信息
        lines.append("## 查询详情")
        lines.append("### 原始查询:")
        lines.append(result.original_query)
        lines.append("")
        
        lines.append("### 子查询:")
        for i, query in enumerate(result.sub_queries, 1):
            lines.append(f"{i}. {query}")
        lines.append("")
        
        if len(result.all_queries) > len(result.sub_queries):
            lines.append("### 生成的新查询:")
            new_queries = result.all_queries[len(result.sub_queries):]
            for i, query in enumerate(new_queries, 1):
                lines.append(f"{i}. {query}")
            lines.append("")
    
    # 文件级结果
    lines.append("## 搜索结果")
    if result.file_level_results:
        for file_path, content in result.file_level_results.items():
            lines.append(f"### 文件: {file_path}")
            lines.append("```")
            lines.append(content)
            lines.append("```")
            lines.append("")
    else:
        lines.append("未找到相关代码片段")
    
    return "\n".join(lines)


def demo():
    """演示函数"""
    print("DeepSearch 演示")
    print("=" * 30)
    
    # 使用当前仓库作为演示
    repo_path = "."
    repo_info = "这是一个包含DeepSearch功能的Python项目，主要模块包括deep_search核心搜索、llm_client大模型调用、search搜索引擎等。"
    
    # 创建DeepSearch实例
    deep_search = DeepSearch(
        repo_path=repo_path,
        repo_info=repo_info
    )
    
    # 演示查询
    demo_queries = [
        "如何初始化LLM客户端?",
        "搜索引擎是如何工作的?",
        "DeepSearch的主要流程是什么?"
    ]
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n{i}. 演示查询: {query}")
        print("-" * 40)
        
        try:
            result = deep_search.search(query)
            print(result.get_summary())
            
            # 显示部分结果
            if result.file_level_results:
                file_count = len(result.file_level_results)
                print(f"找到 {file_count} 个相关文件的代码片段")
        except Exception as e:
            print(f"查询失败: {e}")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数时运行演示
        demo()
    else:
        # 有参数时运行主程序
        main()
